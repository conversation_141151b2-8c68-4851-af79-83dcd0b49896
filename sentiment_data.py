import torch
import numpy as np
import pandas as pd
from collections import Counter
from nltk import word_tokenize
from datasets import load_dataset
import os

def seq_padding(X, pad_id=0):
    L = [len(x) for x in X]
    ML = max(L)
    return np.array([
        np.concatenate([x, [pad_id] * (ML - len(x))]) if len(x) < ML else x for x in X
    ])

class SentimentMaskBatch:
    def __init__(self, src, labels, pad_id=0):
        src = torch.from_numpy(src).long()
        labels = torch.from_numpy(labels).long()
        self.src = src
        self.labels = labels
        self.src_mask = (src != pad_id).unsqueeze(1).unsqueeze(1)

class SentimentDataPreparation:
    def __init__(self, batch_size, max_length=128, unk_id=1, pad_id=0, vocab_dict_path=None):
        self.unk_id = unk_id
        self.pad_id = pad_id
        self.max_length = max_length
        self.batch_size = batch_size
        
        print("Loading sentiment dataset...")
        self.train_texts, self.train_labels, self.test_texts, self.test_labels = self.load_sentiment_data()
        
        if vocab_dict_path and os.path.exists(vocab_dict_path):
            print(f"Loading existing vocabulary from {vocab_dict_path}")
            self.word_dict, self.total_words, self.index_dict = self.load_vocab(vocab_dict_path)
        else:
            print("Building vocabulary from training data...")
            self.word_dict, self.total_words, self.index_dict = self.build_dict(self.train_texts)
            if vocab_dict_path:
                self.save_vocab(vocab_dict_path)
        
        print(f"Vocabulary size: {self.total_words}")
        
        self.train_ids = self.text_to_ids(self.train_texts)
        self.test_ids = self.text_to_ids(self.test_texts)
        
        self.train_data = self.create_batches(self.train_ids, self.train_labels, shuffle=True)
        self.test_data = self.create_batches(self.test_ids, self.test_labels, shuffle=False)
        
        print(f"Training batches: {len(self.train_data)}")
        print(f"Test batches: {len(self.test_data)}")
    
    def load_sentiment_data(self):
        dataset = load_dataset("mteb/tweet_sentiment_extraction")
        
        train_texts = [text for text in dataset['train']['text']]
        train_labels = [label for label in dataset['train']['label']]
        test_texts = [text for text in dataset['test']['text']]
        test_labels = [label for label in dataset['test']['label']]
        
        print(f"Train samples: {len(train_texts)}")
        print(f"Test samples: {len(test_texts)}")
        print(f"Label distribution - Train: {Counter(train_labels)}")
        print(f"Label distribution - Test: {Counter(test_labels)}")
        
        return train_texts, train_labels, test_texts, test_labels
    
    def preprocess_text(self, text):
        if not text or pd.isna(text):
            return []
        try:
            tokens = word_tokenize(text.lower())
            return tokens[:self.max_length-2]
        except:
            return []
    
    def build_dict(self, texts, max_words=30000):
        word_count = Counter()
        for text in texts:
            tokens = self.preprocess_text(text)
            for token in tokens:
                word_count[token] += 1
        
        ls = word_count.most_common(max_words)
        total_words = len(ls) + 2
        word_dict = {w[0]: index + 2 for index, w in enumerate(ls)}
        word_dict['UNK'] = self.unk_id
        word_dict['PAD'] = self.pad_id
        index_dict = {v: k for k, v in word_dict.items()}
        return word_dict, total_words, index_dict
    
    def text_to_ids(self, texts):
        ids_list = []
        for text in texts:
            tokens = self.preprocess_text(text)
            ids = [self.word_dict.get(token, self.unk_id) for token in tokens]
            ids_list.append(ids)
        return ids_list
    
    def create_batches(self, text_ids, labels, shuffle=True):
        data_pairs = list(zip(text_ids, labels))
        if shuffle:
            np.random.shuffle(data_pairs)
        
        batches = []
        for i in range(0, len(data_pairs), self.batch_size):
            batch_pairs = data_pairs[i:i + self.batch_size]
            batch_texts, batch_labels = zip(*batch_pairs)
            
            batch_texts_padded = seq_padding(batch_texts, pad_id=self.pad_id)
            batch_labels_array = np.array(batch_labels)
            
            batches.append(SentimentMaskBatch(batch_texts_padded, batch_labels_array, self.pad_id))
        
        return batches
    
    def save_vocab(self, path):
        torch.save({
            'word_dict': self.word_dict,
            'total_words': self.total_words,
            'index_dict': self.index_dict
        }, path)
        print(f"Vocabulary saved to {path}")
    
    def load_vocab(self, path):
        vocab_data = torch.load(path)
        return vocab_data['word_dict'], vocab_data['total_words'], vocab_data['index_dict']
