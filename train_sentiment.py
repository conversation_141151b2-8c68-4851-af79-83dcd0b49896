#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import os
import warnings
warnings.filterwarnings('ignore')

from model.sentiment_transformer import build_sentiment_transformer
from sentiment_data import SentimentDataPreparation

def get_config():
    return {
        'lr': 2e-5,
        'batch_size': 32,
        'num_epochs': 10,
        'n_layer': 6,
        'h_num': 8,
        'd_model': 256,
        'd_ff': 1024,
        'dropout': 0.1,
        'max_length': 128,
        'num_classes': 3,
        'pretrained_model': 'save/models/model.pt',
        'save_dir': 'save/sentiment_models',
        'vocab_path': 'save/sentiment_models/vocab.pt'
    }

def calculate_accuracy(predictions, labels):
    pred_classes = torch.argmax(predictions, dim=1)
    correct = (pred_classes == labels).float()
    return correct.mean().item()

def evaluate_model(model, data_loader, loss_fn, device):
    model.eval()
    total_loss = 0
    total_accuracy = 0
    num_batches = 0
    
    with torch.no_grad():
        for batch in data_loader:
            src = batch.src.to(device)
            labels = batch.labels.to(device)
            src_mask = batch.src_mask.to(device)
            
            outputs = model(src, src_mask)
            loss = loss_fn(outputs, labels)
            accuracy = calculate_accuracy(outputs, labels)
            
            total_loss += loss.item()
            total_accuracy += accuracy
            num_batches += 1
    
    return total_loss / num_batches, total_accuracy / num_batches

def plot_training_curves(train_losses, val_losses, val_accuracies, save_path):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    epochs = range(1, len(train_losses) + 1)
    
    ax1.plot(epochs, train_losses, 'b-', label='Training Loss')
    ax1.plot(epochs, val_losses, 'r-', label='Validation Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)
    
    ax2.plot(epochs, val_accuracies, 'g-', label='Validation Accuracy')
    ax2.set_title('Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    print(f"Training curves saved to {save_path}")

def main():
    config = get_config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    os.makedirs(config['save_dir'], exist_ok=True)
    
    print("Preparing sentiment analysis data...")
    data_prep = SentimentDataPreparation(
        batch_size=config['batch_size'],
        max_length=config['max_length'],
        vocab_dict_path=config['vocab_path']
    )
    
    print("Building sentiment transformer model...")
    model = build_sentiment_transformer(
        vocab_size=data_prep.total_words,
        seq_len=config['max_length'],
        num_classes=config['num_classes'],
        d_model=config['d_model'],
        N=config['n_layer'],
        h=config['h_num'],
        dropout=config['dropout'],
        d_ff=config['d_ff']
    ).to(device)
    
    if os.path.exists(config['pretrained_model']):
        print(f"Loading pretrained encoder from {config['pretrained_model']}")
        model.load_pretrained_encoder(config['pretrained_model'], device)
    else:
        print("Warning: Pretrained model not found. Training from scratch.")
    
    loss_fn = nn.CrossEntropyLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=0.01)
    
    train_losses = []
    val_losses = []
    val_accuracies = []
    best_accuracy = 0.0
    
    print("Starting training...")
    for epoch in range(config['num_epochs']):
        model.train()
        epoch_loss = 0
        num_batches = 0
        
        batch_iterator = tqdm(data_prep.train_data, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch in batch_iterator:
            src = batch.src.to(device)
            labels = batch.labels.to(device)
            src_mask = batch.src_mask.to(device)
            
            optimizer.zero_grad()
            outputs = model(src, src_mask)
            loss = loss_fn(outputs, labels)
            
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            num_batches += 1
            
            batch_iterator.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = epoch_loss / num_batches
        train_losses.append(avg_train_loss)
        
        val_loss, val_accuracy = evaluate_model(model, data_prep.test_data, loss_fn, device)
        val_losses.append(val_loss)
        val_accuracies.append(val_accuracy)
        
        print(f"Epoch {epoch+1}: Train Loss: {avg_train_loss:.4f}, "
              f"Val Loss: {val_loss:.4f}, Val Accuracy: {val_accuracy:.4f}")
        
        if val_accuracy > best_accuracy:
            best_accuracy = val_accuracy
            best_model_path = os.path.join(config['save_dir'], 'best_sentiment_model.pt')
            torch.save(model.state_dict(), best_model_path)
            print(f"New best model saved with accuracy: {best_accuracy:.4f}")
        
        model_path = os.path.join(config['save_dir'], f'sentiment_model_epoch_{epoch+1}.pt')
        torch.save(model.state_dict(), model_path)
    
    curves_path = os.path.join(config['save_dir'], 'training_curves.png')
    plot_training_curves(train_losses, val_losses, val_accuracies, curves_path)
    
    print(f"Training completed! Best validation accuracy: {best_accuracy:.4f}")
    print(f"Models saved in: {config['save_dir']}")

if __name__ == "__main__":
    main()
