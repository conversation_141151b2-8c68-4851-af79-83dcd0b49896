#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import os

from model.sentiment_transformer import build_sentiment_transformer
from sentiment_data import SentimentDataPreparation

def get_config():
    return {
        'batch_size': 32,
        'max_length': 128,
        'n_layer': 6,
        'h_num': 8,
        'd_model': 256,
        'd_ff': 1024,
        'dropout': 0.1,
        'num_classes': 3,
        'model_path': 'save/sentiment_models/best_sentiment_model.pt',
        'vocab_path': 'save/sentiment_models/vocab.pt',
        'results_dir': 'save/sentiment_models'
    }

def evaluate_detailed(model, data_loader, device):
    model.eval()
    all_predictions = []
    all_labels = []
    all_probabilities = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Evaluating"):
            src = batch.src.to(device)
            labels = batch.labels.to(device)
            src_mask = batch.src_mask.to(device)
            
            outputs = model(src, src_mask)
            probabilities = torch.softmax(outputs, dim=1)
            predictions = torch.argmax(outputs, dim=1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
    
    return np.array(all_predictions), np.array(all_labels), np.array(all_probabilities)

def plot_confusion_matrix(y_true, y_pred, save_path):
    cm = confusion_matrix(y_true, y_pred)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Negative', 'Neutral', 'Positive'],
                yticklabels=['Negative', 'Neutral', 'Positive'])
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
    print(f"Confusion matrix saved to {save_path}")

def analyze_predictions(predictions, labels, probabilities, data_prep, save_path):
    label_names = ['Negative', 'Neutral', 'Positive']
    
    report = classification_report(labels, predictions, 
                                 target_names=label_names, 
                                 output_dict=True)
    
    print("\nClassification Report:")
    print(classification_report(labels, predictions, target_names=label_names))
    
    accuracy = (predictions == labels).mean()
    print(f"\nOverall Accuracy: {accuracy:.4f}")
    
    confidence_scores = np.max(probabilities, axis=1)
    avg_confidence = np.mean(confidence_scores)
    print(f"Average Confidence: {avg_confidence:.4f}")
    
    results = {
        'accuracy': accuracy,
        'avg_confidence': avg_confidence,
        'classification_report': report,
        'predictions': predictions.tolist(),
        'labels': labels.tolist(),
        'probabilities': probabilities.tolist()
    }
    
    torch.save(results, save_path)
    print(f"Detailed results saved to {save_path}")
    
    return results

def test_sample_predictions(model, data_prep, device, num_samples=10):
    model.eval()
    
    print(f"\nSample Predictions (first {num_samples} test examples):")
    print("-" * 80)
    
    label_names = ['Negative', 'Neutral', 'Positive']
    
    with torch.no_grad():
        for i, batch in enumerate(data_prep.test_data):
            if i >= num_samples:
                break
                
            src = batch.src.to(device)
            labels = batch.labels.to(device)
            src_mask = batch.src_mask.to(device)
            
            outputs = model(src, src_mask)
            probabilities = torch.softmax(outputs, dim=1)
            predictions = torch.argmax(outputs, dim=1)
            
            for j in range(min(len(src), num_samples - i)):
                if i + j >= num_samples:
                    break
                    
                text_ids = src[j].cpu().numpy()
                text_tokens = [data_prep.index_dict.get(id, 'UNK') for id in text_ids if id != 0]
                text = ' '.join(text_tokens)
                
                true_label = label_names[labels[j].item()]
                pred_label = label_names[predictions[j].item()]
                confidence = probabilities[j].max().item()
                
                print(f"Sample {i+j+1}:")
                print(f"Text: {text[:100]}...")
                print(f"True: {true_label}, Predicted: {pred_label}, Confidence: {confidence:.3f}")
                print("-" * 40)

def main():
    config = get_config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if not os.path.exists(config['model_path']):
        print(f"Error: Model file not found at {config['model_path']}")
        print("Please train the model first using train_sentiment.py")
        return
    
    print("Loading sentiment analysis data...")
    data_prep = SentimentDataPreparation(
        batch_size=config['batch_size'],
        max_length=config['max_length'],
        vocab_dict_path=config['vocab_path']
    )
    
    print("Building and loading model...")
    model = build_sentiment_transformer(
        vocab_size=data_prep.total_words,
        seq_len=config['max_length'],
        num_classes=config['num_classes'],
        d_model=config['d_model'],
        N=config['n_layer'],
        h=config['h_num'],
        dropout=config['dropout'],
        d_ff=config['d_ff']
    ).to(device)
    
    model.load_state_dict(torch.load(config['model_path'], map_location=device))
    print(f"Model loaded from {config['model_path']}")
    
    print("Evaluating model...")
    predictions, labels, probabilities = evaluate_detailed(model, data_prep.test_data, device)
    
    cm_path = os.path.join(config['results_dir'], 'confusion_matrix.png')
    plot_confusion_matrix(labels, predictions, cm_path)
    
    results_path = os.path.join(config['results_dir'], 'evaluation_results.pt')
    results = analyze_predictions(predictions, labels, probabilities, data_prep, results_path)
    
    test_sample_predictions(model, data_prep, device)
    
    print(f"\nEvaluation completed!")
    print(f"Results saved in: {config['results_dir']}")

if __name__ == "__main__":
    main()
