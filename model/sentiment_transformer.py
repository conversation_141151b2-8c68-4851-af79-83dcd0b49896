import torch
import torch.nn as nn
from .transformer import (
    Encoder, EncoderBlock, MultiHeadAttentionBlock,
    FeedForwardBlock, InputEmbeddings, LearnablePositionalEncoding,
    LayerNormalization
)

class SentimentClassificationHead(nn.Module):
    def __init__(self, d_model: int, num_classes: int, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(d_model, num_classes)

    def forward(self, x):
        x = x.mean(dim=1)
        x = self.dropout(x)
        return self.classifier(x)

class SentimentTransformer(nn.Module):
    def __init__(self, encoder: Encoder, src_embed: InputEmbeddings,
                 src_pos: LearnablePositionalEncoding, classification_head: SentimentClassificationHead):
        super().__init__()
        self.encoder = encoder
        self.src_embed = src_embed
        self.src_pos = src_pos
        self.classification_head = classification_head

    def forward(self, src, src_mask):
        src = self.src_embed(src)
        src = self.src_pos(src)
        encoder_output = self.encoder(src, src_mask)
        return self.classification_head(encoder_output)

    def load_pretrained_encoder(self, pretrained_model_path: str, device: str = 'cpu'):
        checkpoint = torch.load(pretrained_model_path, map_location=device)

        model_state = self.state_dict()
        loaded_keys = []

        for key, value in checkpoint.items():
            if key.startswith('encoder.'):
                if key in model_state and model_state[key].shape == value.shape:
                    model_state[key] = value
                    loaded_keys.append(key)
                else:
                    print(f"Skipping {key}: shape mismatch or not found")

        self.load_state_dict(model_state, strict=False)
        print(f"Loaded {len(loaded_keys)} encoder layers from {pretrained_model_path}")
        print("Note: Embeddings and positional encodings will be trained from scratch due to different vocab/sequence length")

def build_sentiment_transformer(vocab_size: int, seq_len: int, num_classes: int = 3,
                               d_model: int = 512, N: int = 6, h: int = 8,
                               dropout: float = 0.1, d_ff: int = 2048) -> SentimentTransformer:

    src_embed = InputEmbeddings(d_model, vocab_size)
    src_pos = LearnablePositionalEncoding(d_model, seq_len, dropout)

    encoder_blocks = []
    for _ in range(N):
        encoder_self_attention_block = MultiHeadAttentionBlock(d_model, h, dropout)
        feed_forward_block = FeedForwardBlock(d_model, d_ff, dropout)
        encoder_block = EncoderBlock(encoder_self_attention_block, feed_forward_block, dropout)
        encoder_blocks.append(encoder_block)

    encoder = Encoder(nn.ModuleList(encoder_blocks))
    classification_head = SentimentClassificationHead(d_model, num_classes, dropout)

    sentiment_transformer = SentimentTransformer(encoder, src_embed, src_pos, classification_head)

    for p in sentiment_transformer.parameters():
        if p.dim() > 1:
            nn.init.xavier_uniform_(p)

    return sentiment_transformer
